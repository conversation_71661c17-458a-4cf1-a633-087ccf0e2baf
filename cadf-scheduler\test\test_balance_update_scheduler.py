import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from omni.mongo.mongo_client import init_models
from scheduler.other.balance_update_scheduler import execute_task


async def main():
    """简单运行余额更新任务"""
    print("正在初始化数据库连接...")
    await init_models()

    print("开始执行余额更新任务...")
    await execute_task()
    print("任务执行完成！")


if __name__ == "__main__":
    asyncio.run(main())
