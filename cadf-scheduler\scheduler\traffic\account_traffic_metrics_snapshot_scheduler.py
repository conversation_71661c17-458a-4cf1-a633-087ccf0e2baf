import time
from collections import defaultdict
from typing import Dict, List

from beanie.operators import In

from models.models import Product, ProductTrafficMetrics, AccountTrafficMetrics
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """处理广告主产品流量统计"""
    olog.info("开始执行产品流量统计任务")

    # 查询所有未删除的产品
    products: List[Product] = await Product.find(Product.is_deleted == False).to_list()
    olog.debug(f"查询到 {len(products)} 个产品")

    product_ids: List[str] = [str(product.id) for product in products]

    # 批量查询账号流量指标
    all_account_metrics: List[AccountTrafficMetrics] = await AccountTrafficMetrics.find(
        In(AccountTrafficMetrics.product_id, product_ids)
    ).to_list()
    olog.debug(f"查询到 {len(all_account_metrics)} 条账号流量数据")

    # 按产品ID分组
    metrics_by_product: Dict[str, List[AccountTrafficMetrics]] = defaultdict(list)
    for metric in all_account_metrics:
        metrics_by_product[metric.product_id].append(metric)

    processed_count: int = 0
    current_time: int = int(time.time())

    for product in products:
        product_id_str: str = str(product.id)
        account_metrics: List[AccountTrafficMetrics] = metrics_by_product[product_id_str]

        # 按平台分组统计
        platform_metrics: Dict[str, List[AccountTrafficMetrics]] = defaultdict(list)
        for metric in account_metrics:
            if metric.platform:
                platform_metrics[metric.platform].append(metric)

        # 为每个平台创建独立的统计记录
        for platform, platform_account_metrics in platform_metrics.items():
            total_view_count: int = sum(metric.view_count or 0 for metric in platform_account_metrics)
            total_like_count: int = sum(metric.like_count or 0 for metric in platform_account_metrics)
            total_comment_count: int = sum(metric.comment_count or 0 for metric in platform_account_metrics)
            total_favorite_count: int = sum(metric.favorite_count or 0 for metric in platform_account_metrics)
            total_share_count: int = sum(metric.share_count or 0 for metric in platform_account_metrics)

            # 更新该产品在该平台的流量统计
            existing_metric = await ProductTrafficMetrics.find_one(
                ProductTrafficMetrics.user_id == product.user_id,
                ProductTrafficMetrics.product_id == product_id_str,
                ProductTrafficMetrics.platform == platform
            )

            if existing_metric:
                # 更新已存在的记录
                await existing_metric.update({
                    "$set": {
                        "total_view_count": total_view_count,
                        "total_like_count": total_like_count,
                        "total_comment_count": total_comment_count,
                        "total_favorite_count": total_favorite_count,
                        "total_share_count": total_share_count,
                        "last_updated_at": current_time
                    }
                })
            else:
                # 创建新记录
                new_metric = ProductTrafficMetrics(
                    user_id=product.user_id,
                    product_id=product_id_str,
                    platform=platform,
                    total_view_count=total_view_count,
                    total_like_count=total_like_count,
                    total_comment_count=total_comment_count,
                    total_favorite_count=total_favorite_count,
                    total_share_count=total_share_count,
                    last_updated_at=current_time
                )
                await new_metric.insert()

            olog.debug(f"产品 {product_id_str} 在 {platform} 平台的流量统计已更新")

        processed_count += 1

    olog.info(f"产品流量统计任务完成，处理了 {processed_count} 个产品")


# 调度频率：每2小时执行一次
@register_scheduler(trigger="cron", hour="*/2", minute="0")
class AccountTrafficMetricsSnapshotScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """执行广告主产品流量统计任务，每2小时执行一次"""
        await execute_task()


if __name__ == "__main__":
    import asyncio


    async def main() -> None:
        await init_models()
        await execute_task()


    asyncio.run(main())
